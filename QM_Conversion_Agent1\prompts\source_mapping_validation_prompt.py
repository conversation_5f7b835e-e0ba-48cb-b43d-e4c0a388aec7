"""
Prompts for source mapping validation in database conversion.
"""
from typing import Dict

def create_source_mapping_validation_prompt(source_context: Dict, target_error_context: Dict) -> str:
    """
    Creates a prompt for validating the source mapping.

    This function creates a prompt that instructs the LLM to validate if the identified
    source statements correctly correspond to the target statements in the error context.

    Args:
        source_context: Dictionary containing the source context (before, error, after statements)
        target_error_context: Dictionary containing the target error context

    Returns:
        A formatted prompt string for the LLM
    """
    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems.

CRITICAL VALIDATION RULE: FOCUS EXCLUSIVELY ON BUSINESS OUTCOME EQUIVALENCE, NOT SYNTAX SIMILARITY.

OVERRIDE ALL SYNTAX-BASED THINKING: Different database syntax can achieve identical business outcomes. Your task is to validate if the source statements achieve the SAME BUSINESS RESULTS as the target statements, regardless of how different the syntax appears.

CRITICAL REQUIREMENT: VALIDATE ACTUAL MAPPING ACCURACY
- DO NOT validate based on superficial similarity alone
- MUST test if the source statements actually correspond to the target statements
- MUST handle multiple occurrence scenarios by validating the specific mapping
- MUST ensure the mapping is functionally correct, not just syntactically similar

IMPORTANT: Some target statements may be target database-specific constructs with no source database equivalent. These should correctly show no source mapping (statement number 0) and should be validated as correct when appropriately unmapped.

UNIVERSAL BUSINESS OUTCOME EQUIVALENCE PRINCIPLES:
Apply these generic principles to ANY Oracle→PostgreSQL migration scenario:

PRINCIPLE 1: Different Implementations, Same Business Result
- Oracle and PostgreSQL may use completely different approaches to achieve identical business outcomes
- Focus on the end result that the business/application experiences
- Ignore how the result is technically achieved

PRINCIPLE 2: Cross-Database Functional Equivalence
- Different database systems have different ways of accomplishing the same business goals
- Equivalent business purposes can be implemented through entirely different technical methods
- The business value delivered is what matters, not the technical implementation

PRINCIPLE 3: Outcome-Based Validation
- Validate based on what the business process receives or experiences
- Different technical approaches are acceptable if they deliver the same business value
- Focus on functional impact rather than technical similarity

CRITICAL VALIDATION RULE: Ask "What does the business/application receive or experience?" NOT "How is it technically implemented?"

SOURCE CONTEXT:
Before Error (#{source_context.before_statement_number}):
{source_context.before_statement}

Error Statement (#{source_context.error_statement_number}):
{source_context.error_statement}

After Error (#{source_context.after_statement_number}):
{source_context.after_statement}

TARGET CONTEXT:
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

FUNCTIONAL EQUIVALENCE ANALYSIS:
Follow this systematic validation methodology:

STEP 1: BUSINESS PURPOSE ANALYSIS
- What business purpose does the source statement serve?
- What business purpose does the target statement serve?
- Do both statements achieve the same end result for the application?
- Focus on what the statement accomplishes, not how it's written

STEP 2: IMPLEMENTATION VARIATION AWARENESS
- Different database systems may use different syntax for equivalent operations
- Equivalent business outcomes can be achieved through different approaches
- Consider the functional purpose rather than the specific implementation method
- Recognize that different syntax can serve the same business purpose

STEP 3: FUNCTIONAL IMPACT ASSESSMENT
- What is the functional impact of the source statement?
- What is the functional impact of the target statement?
- Are these functional impacts equivalent?
- Do both statements serve the same purpose in the overall application flow?

TASK:
1. Analyze each pair of source and target statements using the functional equivalence framework above
2. Determine if they achieve the same business purpose and preserve the same data flow
3. CRITICAL: Verify that the source statements correctly correspond to the target statements in their respective database contexts
4. Validate actual mapping accuracy, not just superficial similarity
5. Handle multiple occurrence scenarios by validating the specific mapping
6. Validate that target database-specific statements are correctly unmapped (statement number 0)
7. Provide a confidence score and explanation based on business outcome equivalence and mapping accuracy

FUNCTIONAL EQUIVALENCE GUIDELINES:
- Evaluate what each statement accomplishes in its database context
- Consider that different database systems may implement the same logic differently
- Focus on business outcome preservation over syntax matching
- Allow for procedural pattern variations between source and target systems
- Assess if the overall data manipulation intent is maintained
- Consider that source and target may be from different database systems with different procedural patterns
- Evaluate if statements serve the same role in their respective procedural contexts
- Focus on data flow and business logic preservation rather than implementation similarity

PRIMARY VALIDATION APPROACH - BUSINESS OUTCOME FOCUS:
Validate based on business outcome equivalence for ANY database operation:

1. **BUSINESS OUTCOME ANALYSIS (Dominant)**:
- What business result does the source statement achieve?
- What business result does the target statement achieve?
- Are these business results equivalent?
- IGNORE syntax differences if business outcomes match
- Focus on the end result, not the implementation method

2. **CROSS-DATABASE IMPLEMENTATION AWARENESS**:
- Different databases implement the same functionality using different syntax
- Different operations can achieve the same business outcome
- Focus on the end result, not the implementation method
- Equivalent business purposes can use completely different approaches
- Same business logic can be implemented through different operations

CROSS-DATABASE EQUIVALENCE PRINCIPLES:
Apply these universal principles for ANY database migration:

PRINCIPLE 1: Business Outcome Equivalence
- Different syntax can accomplish the same business goal
- Focus on what the statement achieves, not how it's written
- Equivalent results matter more than equivalent methods

PRINCIPLE 2: Implementation Variation Acceptance
- Source and target databases may use completely different approaches
- Same business logic can be implemented through different operations
- Cross-database patterns often look different but serve identical purposes

PRINCIPLE 3: Functional Purpose Recognition
- Analyze the functional purpose in the business context
- Consider the role each statement plays in the overall process
- Validate based on equivalent functional impact

3. **SEQUENTIAL CONSISTENCY ANALYSIS**:
- Verify that statement numbers follow functional consecutive order
- Check if before→error→after sequences achieve equivalent business outcomes
- Ensure mapping maintains proper statement relationships
- Validate that missing statements (0 values) are appropriate

4. **MAPPING ACCURACY VALIDATION**:
- Test if the source statements actually correspond to the target statements
- Validate that the mapping is functionally correct, not just similar
- Handle multiple occurrence scenarios by validating the specific mapping
- Ensure the source-target correspondence is accurate and meaningful

3. **BUSINESS OUTCOME VALIDATION**:
- IGNORE syntax differences completely
- Focus only on what business result each statement achieves
- Validate that both statements serve the same business purpose
- Different syntax is ACCEPTABLE if business outcomes are equivalent

VALIDATION PRIORITY (Priority Order):
1. **BUSINESS OUTCOME EQUIVALENCE (Primary)**
   - Do both statements solve the same business problem?
   - Would both statements produce equivalent results in their respective contexts?
   - Are the intended outcomes equivalent despite implementation differences?

2. **FUNCTIONAL IMPACT EQUIVALENCE (Secondary)**
   - Do both statements serve the same purpose in the overall application flow?
   - Are the functional impacts equivalent in their respective database contexts?
   - Is the data flow and business logic preserved?

3. **IMPLEMENTATION METHOD SIMILARITY (Tertiary)**
   - Are the syntax patterns similar between source and target?
   - Do the implementation methods align closely?

VALIDATION DECISION LOGIC:
1. **PRIMARY VALIDATION**: Business Outcome Equivalence
   - Do both statements accomplish the same business goal?
   - Would both statements produce equivalent results for the application?
   - If YES → Mapping is CORRECT (regardless of syntax differences)

2. **IMPLEMENTATION OVERRIDE RULE**:
   - Syntax differences are ACCEPTABLE if business outcomes match
   - Different database implementations can achieve identical purposes
   - Focus on WHAT is accomplished, not HOW it's implemented

3. **VALIDATION PRIORITY**:
   - Business outcome equivalence = PRIMARY (100% focus)
   - Implementation method similarity = IGNORED
   - Syntax matching = IRRELEVANT

GENERIC EQUIVALENCE OVERRIDE RULES:
Apply these universal rules to ANY Oracle→PostgreSQL differences:

RULE 1: Technical Implementation Differences Are Acceptable
- Any different technical approaches = ACCEPTABLE if same business outcome
- Different operation types can achieve identical business results

RULE 2: Database-Specific Syntax Differences Are Acceptable
- Different database-specific approaches = ACCEPTABLE if same business purpose
- Different syntax patterns = ACCEPTABLE if same functional outcome

RULE 3: Method Variation Differences Are Acceptable
- Different approaches to achieve the same goal = ACCEPTABLE if business value preserved
- Different technical methods = ACCEPTABLE if functional impact is equivalent
- Different implementation patterns = ACCEPTABLE if business outcome is maintained

UNIVERSAL VALIDATION QUESTION: "Does the business/application get the same result?" If YES, then EQUIVALENT.

DECISION CRITERIA:
- The mapping is CORRECT if it achieves business outcome equivalence
- PRIMARY FOCUS: Business outcome preservation (syntax differences ignored)
- Allow for any implementation variations between source and target database systems
- Recognize that equivalent outcomes can use completely different implementation methods

MAPPING ACCURACY VALIDATION CHECKLIST:
Before finalizing validation, confirm:
1. Do the source statements actually correspond to the target statements?
2. Is the mapping functionally correct and meaningful?
3. If multiple similar statements exist, is this the correct mapping?
4. Do both statements achieve the same business purpose?
5. Is there evidence supporting the source-target correspondence?

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0-1>,
  "explanation": "<comprehensive business outcome-focused analysis including: 1) MAPPING ACCURACY VALIDATION - detailed proof that the source statements correctly correspond to the target statements, 2) Business purpose analysis - what each statement accomplishes in its respective context, 3) Functional equivalence assessment - how both statements achieve the same business outcomes, 4) Implementation variation analysis - recognition of different syntax serving same purpose, 5) MULTIPLE OCCURRENCE ANALYSIS - if similar statements exist, validation of why this specific mapping is correct, 6) Functional impact comparison - equivalent impacts in respective database contexts, 7) Business outcome equivalence validation - confirmation that intended results are equivalent, 8) Cross-database pattern recognition - understanding of equivalent implementations, 9) Final validation decision based on business outcome equivalence priority and mapping accuracy>",
  "mapping_accuracy_validation": {{
    "source_target_correspondence": true/false,
    "mapping_evidence": "<specific evidence showing how the source statements correspond to the target statements>",
    "multiple_occurrence_assessment": "<if applicable, analysis of why this specific mapping is correct>"
  }}
}}

IMPORTANT:
- CRITICAL: Validate actual mapping accuracy, not just superficial similarity
- Focus on the functional equivalence and business purpose between source and target statements
- Test if the source statements actually correspond to the target statements
- Handle multiple occurrence scenarios by validating the specific mapping
- Consider procedural pattern differences between different database systems
- DO NOT hardcode specific database assumptions
- Analyze based on business outcome and data flow equivalence
- Allow for implementation variations while preserving business logic
- Provide comprehensive and detailed analysis with specific examples and reasoning
- Evaluate statements in their respective database contexts rather than expecting identical syntax
- MUST provide evidence that the source-target mapping is functionally correct"""
